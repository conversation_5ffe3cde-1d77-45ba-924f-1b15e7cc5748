<?php
require '../../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['userId'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$userId = $_SESSION['userId'];
$chatId = isset($_GET['chatId']) ? intval($_GET['chatId']) : 0;

if ($chatId <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid chat ID']);
    exit;
}

// Verify that the chat belongs to the user
$chatQuery = "SELECT id FROM chats WHERE id = ? AND user_id = ?";
$chatStmt = $db->prepare($chatQuery);
$chatStmt->bind_param("ii", $chatId, $userId);
$chatStmt->execute();
$chatResult = $chatStmt->get_result();

if ($chatResult->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'Chat not found or access denied']);
    exit;
}

// Get messages for the chat
$query = "SELECT id, message_type, content, created_at FROM messages WHERE chat_id = ? ORDER BY created_at ASC";
$stmt = $db->prepare($query);
$stmt->bind_param("i", $chatId);
$stmt->execute();
$result = $stmt->get_result();

$messages = [];
while ($row = $result->fetch_assoc()) {
    $messages[] = [
        'id' => $row['id'],
        'type' => $row['message_type'],
        'content' => $row['content'],
        'timestamp' => $row['created_at']
    ];
}

echo json_encode(['success' => true, 'messages' => $messages]);

$stmt->close();
$chatStmt->close();
?>
